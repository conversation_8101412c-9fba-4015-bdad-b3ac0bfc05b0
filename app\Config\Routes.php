<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
$routes->get('/', 'Home::index');

// Authentication routes
$routes->post('login', 'Auth::login');
$routes->get('logout', 'Auth::logout');
$routes->get('dashboard', 'Auth::dashboard');

// Setup routes (development only)
$routes->get('setup', 'Setup::index');
$routes->get('setup/testConnection', 'Setup::testConnection');
$routes->get('setup/createInitialData', 'Setup::createInitialData');

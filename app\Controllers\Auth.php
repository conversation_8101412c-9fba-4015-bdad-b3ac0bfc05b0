<?php

namespace App\Controllers;

use App\Models\UserModel;

class Auth extends BaseController
{
    protected $userModel;

    public function __construct()
    {
        $this->userModel = new UserModel();
    }

    public function login()
    {
        // Handle login form submission
        if ($this->request->getMethod() === 'POST') {
            $email = $this->request->getPost('email');
            $password = $this->request->getPost('password');
            $remember = $this->request->getPost('remember');

            // Basic validation
            $validation = \Config\Services::validation();
            $validation->setRules([
                'email' => 'required|valid_email',
                'password' => 'required|min_length[6]'
            ]);

            if (!$validation->withRequest($this->request)->run()) {
                $data = [
                    'title' => 'Dakoii Commodity Buyer',
                    'description' => 'Modern commodity trading platform for agricultural products',
                    'errors' => $validation->getErrors()
                ];
                return view('landing_page', $data);
            }

            // Authenticate user with Supabase
            $user = $this->userModel->findByEmail($email);

            if ($user && $user['status'] === 'active') {
                // Verify password
                if ($this->userModel->verifyPassword($password, $user['password'])) {
                    // Set session data
                    session()->set([
                        'user_id' => $user['id'],
                        'email' => $user['email'],
                        'first_name' => $user['first_name'],
                        'last_name' => $user['last_name'],
                        'role' => $user['role'],
                        'logged_in' => true
                    ]);

                    // Set remember me cookie if requested
                    if ($remember) {
                        $this->setRememberMeCookie($user['id']);
                    }

                    return redirect()->to('/dashboard')->with('success', 'Login successful!');
                } else {
                    return redirect()->back()->withInput()->with('error', 'Invalid email or password.');
                }
            } else {
                $message = $user && $user['status'] !== 'active'
                    ? 'Your account is not active. Please contact administrator.'
                    : 'Invalid email or password.';

                return redirect()->back()->withInput()->with('error', $message);
            }
        }

        // If GET request, redirect to home page
        return redirect()->to('/');
    }
    
    public function logout()
    {
        // Clear remember me cookie if it exists
        $this->clearRememberMeCookie();

        // Destroy session
        session()->destroy();

        return redirect()->to('/')->with('success', 'You have been logged out successfully.');
    }

    public function dashboard()
    {
        // Check if user is logged in
        if (!session()->get('logged_in')) {
            // Check for remember me cookie
            if ($this->checkRememberMeCookie()) {
                return redirect()->to('/dashboard');
            }

            return redirect()->to('/')->with('error', 'Please login to access the dashboard.');
        }

        $user = $this->userModel->findById(session()->get('user_id'));

        if (!$user || $user['status'] !== 'active') {
            session()->destroy();
            return redirect()->to('/')->with('error', 'Your account is no longer active.');
        }

        $data = [
            'title' => 'Dashboard - DCBuyer',
            'user_email' => session()->get('email'),
            'user_name' => session()->get('first_name') . ' ' . session()->get('last_name'),
            'user_role' => session()->get('role')
        ];

        return view('dashboard', $data);
    }

    /**
     * Set remember me cookie
     */
    private function setRememberMeCookie(int $userId)
    {
        $token = bin2hex(random_bytes(32));
        $expiry = time() + (30 * 24 * 60 * 60); // 30 days

        // Store token in database (you might want to create a remember_tokens table)
        // For now, we'll use a simple approach with session
        setcookie('remember_token', $token, $expiry, '/', '', false, true);

        // You should store this token in database with user_id for security
        session()->set('remember_token_' . $userId, $token);
    }

    /**
     * Clear remember me cookie
     */
    private function clearRememberMeCookie()
    {
        if (isset($_COOKIE['remember_token'])) {
            setcookie('remember_token', '', time() - 3600, '/', '', false, true);
        }
    }

    /**
     * Check remember me cookie and auto-login if valid
     */
    private function checkRememberMeCookie(): bool
    {
        if (!isset($_COOKIE['remember_token'])) {
            return false;
        }

        $token = $_COOKIE['remember_token'];

        // In a real application, you should verify this token against database
        // For now, we'll use a simple session-based approach
        foreach ($_SESSION as $key => $value) {
            if (strpos($key, 'remember_token_') === 0 && $value === $token) {
                $userId = str_replace('remember_token_', '', $key);
                $user = $this->userModel->findById((int)$userId);

                if ($user && $user['status'] === 'active') {
                    // Auto-login user
                    session()->set([
                        'user_id' => $user['id'],
                        'email' => $user['email'],
                        'first_name' => $user['first_name'],
                        'last_name' => $user['last_name'],
                        'role' => $user['role'],
                        'logged_in' => true
                    ]);

                    return true;
                }
            }
        }

        return false;
    }
}

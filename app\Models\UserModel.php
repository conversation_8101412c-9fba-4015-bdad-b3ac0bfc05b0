<?php

namespace App\Models;

use App\Libraries\SupabaseService;
use CodeIgniter\Model;

class UserModel extends Model
{
    protected $table = 'users';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'email',
        'password',
        'first_name',
        'last_name',
        'role',
        'status',
        'created_at',
        'updated_at'
    ];

    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    protected $validationRules = [
        'email' => 'required|valid_email|is_unique[users.email]',
        'password' => 'required|min_length[8]',
        'first_name' => 'required|min_length[2]|max_length[50]',
        'last_name' => 'required|min_length[2]|max_length[50]',
        'role' => 'required|in_list[admin,buyer,seller]',
        'status' => 'required|in_list[active,inactive,suspended]'
    ];

    protected $validationMessages = [
        'email' => [
            'required' => 'Email is required',
            'valid_email' => 'Please provide a valid email address',
            'is_unique' => 'This email is already registered'
        ],
        'password' => [
            'required' => 'Password is required',
            'min_length' => 'Password must be at least 8 characters long'
        ]
    ];

    private $supabase;

    public function __construct()
    {
        parent::__construct();
        $this->supabase = new SupabaseService();
    }

    /**
     * Find user by email
     */
    public function findByEmail(string $email)
    {
        $result = $this->supabase->select('users', ['*'], ['email' => $email]);
        
        if ($result && count($result) > 0) {
            return $result[0];
        }
        
        return null;
    }

    /**
     * Find user by ID
     */
    public function findById(int $id)
    {
        $result = $this->supabase->select('users', ['*'], ['id' => $id]);
        
        if ($result && count($result) > 0) {
            return $result[0];
        }
        
        return null;
    }

    /**
     * Create a new user
     */
    public function createUser(array $data)
    {
        // Hash password before storing
        if (isset($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        }
        
        // Set default values
        $data['status'] = $data['status'] ?? 'active';
        $data['role'] = $data['role'] ?? 'buyer';
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        return $this->supabase->insert('users', $data);
    }

    /**
     * Update user data
     */
    public function updateUser(int $id, array $data)
    {
        // Hash password if provided
        if (isset($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        }
        
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        return $this->supabase->update('users', $data, ['id' => $id]);
    }

    /**
     * Verify user password
     */
    public function verifyPassword(string $password, string $hashedPassword): bool
    {
        return password_verify($password, $hashedPassword);
    }

    /**
     * Get all users with pagination
     */
    public function getAllUsers(int $limit = 20, int $offset = 0, array $filters = [])
    {
        $options = [
            'limit' => $limit,
            'offset' => $offset,
            'order' => 'created_at.desc'
        ];
        
        return $this->supabase->select('users', ['*'], $filters, $options);
    }

    /**
     * Get users by role
     */
    public function getUsersByRole(string $role)
    {
        return $this->supabase->select('users', ['*'], ['role' => $role]);
    }

    /**
     * Get active users
     */
    public function getActiveUsers()
    {
        return $this->supabase->select('users', ['*'], ['status' => 'active']);
    }

    /**
     * Deactivate user
     */
    public function deactivateUser(int $id)
    {
        return $this->updateUser($id, ['status' => 'inactive']);
    }

    /**
     * Activate user
     */
    public function activateUser(int $id)
    {
        return $this->updateUser($id, ['status' => 'active']);
    }

    /**
     * Delete user (soft delete by setting status to inactive)
     */
    public function deleteUser(int $id)
    {
        return $this->deactivateUser($id);
    }

    /**
     * Check if email exists
     */
    public function emailExists(string $email): bool
    {
        $user = $this->findByEmail($email);
        return $user !== null;
    }

    /**
     * Get user statistics
     */
    public function getUserStats()
    {
        // Get total users
        $totalUsers = $this->supabase->select('users', ['count'], [], ['select' => 'count']);
        
        // Get active users
        $activeUsers = $this->supabase->select('users', ['count'], ['status' => 'active'], ['select' => 'count']);
        
        // Get users by role
        $admins = $this->supabase->select('users', ['count'], ['role' => 'admin'], ['select' => 'count']);
        $buyers = $this->supabase->select('users', ['count'], ['role' => 'buyer'], ['select' => 'count']);
        $sellers = $this->supabase->select('users', ['count'], ['role' => 'seller'], ['select' => 'count']);
        
        return [
            'total' => $totalUsers[0]['count'] ?? 0,
            'active' => $activeUsers[0]['count'] ?? 0,
            'admins' => $admins[0]['count'] ?? 0,
            'buyers' => $buyers[0]['count'] ?? 0,
            'sellers' => $sellers[0]['count'] ?? 0
        ];
    }
}

<?php

namespace App\Controllers;

use App\Models\UserModel;

class Setup extends BaseController
{
    protected $userModel;

    public function __construct()
    {
        $this->userModel = new UserModel();
    }

    /**
     * Create initial admin user and test data
     * This should only be run once during setup
     */
    public function createInitialData()
    {
        // Check if we're in development environment
        if (ENVIRONMENT !== 'development') {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'This endpoint is only available in development environment'
            ]);
        }

        try {
            // Create admin user
            $adminData = [
                'email' => '<EMAIL>',
                'password' => 'password123',
                'first_name' => 'Admin',
                'last_name' => 'User',
                'role' => 'admin',
                'status' => 'active'
            ];

            // Check if admin already exists
            if (!$this->userModel->emailExists($adminData['email'])) {
                $result = $this->userModel->createUser($adminData);
                
                if ($result) {
                    $message[] = 'Admin user created successfully';
                } else {
                    $message[] = 'Failed to create admin user';
                }
            } else {
                $message[] = 'Admin user already exists';
            }

            // Create test buyer
            $buyerData = [
                'email' => '<EMAIL>',
                'password' => 'password123',
                'first_name' => 'John',
                'last_name' => 'Buyer',
                'role' => 'buyer',
                'status' => 'active'
            ];

            if (!$this->userModel->emailExists($buyerData['email'])) {
                $result = $this->userModel->createUser($buyerData);
                
                if ($result) {
                    $message[] = 'Buyer user created successfully';
                } else {
                    $message[] = 'Failed to create buyer user';
                }
            } else {
                $message[] = 'Buyer user already exists';
            }

            // Create test seller
            $sellerData = [
                'email' => '<EMAIL>',
                'password' => 'password123',
                'first_name' => 'Jane',
                'last_name' => 'Seller',
                'role' => 'seller',
                'status' => 'active'
            ];

            if (!$this->userModel->emailExists($sellerData['email'])) {
                $result = $this->userModel->createUser($sellerData);
                
                if ($result) {
                    $message[] = 'Seller user created successfully';
                } else {
                    $message[] = 'Failed to create seller user';
                }
            } else {
                $message[] = 'Seller user already exists';
            }

            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Setup completed',
                'details' => $message ?? []
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Setup error: ' . $e->getMessage());
            
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Setup failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Test Supabase connection
     */
    public function testConnection()
    {
        try {
            // Try to get users (this will test the connection)
            $users = $this->userModel->getAllUsers(1, 0);
            
            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Supabase connection successful',
                'data' => [
                    'users_found' => is_array($users) ? count($users) : 0,
                    'connection_test' => 'passed'
                ]
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Connection test error: ' . $e->getMessage());
            
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Supabase connection failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Display setup instructions
     */
    public function index()
    {
        $data = [
            'title' => 'DCBuyer Setup',
            'supabase_url' => env('supabase.url'),
            'environment' => ENVIRONMENT
        ];

        return view('setup', $data);
    }
}
